import axios from 'axios';
import { getToken } from '../tokenprovider';

const age = import.meta.env.VITE_APP_BACKEND_PORT
console.log("po",age)
export const apiClient = axios.create({
  baseURL: import.meta.env.VITE_APP_BACKEND_PORT,
  timeout: 5000,
  headers: {
    'Content-Type': 'application/json',
  },
});

apiClient.interceptors.request.use(
  (config) => {
    const token = getToken();
    // console.log('Token: ', token);
    if (token) {
      console.log('Tk: ', true);
      // console.log('Token: ', token);
    } else {
      console.log('Tk: ', false);
    }
    if (token) {
      config.headers['Authorization'] = `Bearer ${token} `;
    }
    return config;
  },
  (error) => {
    console.log('axios interceptor error: ', error);
    return Promise.reject(error);
  }
);
