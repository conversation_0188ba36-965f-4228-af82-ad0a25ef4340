import React, { useState, useEffect, useCallback } from 'react';
import { FiEdit, FiTrash, FiCheckCircle, FiXCircle, FiAlertCircle, FiStar, FiEye, FiChevronLeft, FiChevronRight, FiChevronsLeft, FiChevronsRight } from 'react-icons/fi';
import { Pagination, Button, Spinner, Card, CardBody, Chip, Select, SelectItem, Tabs, Tab, Modal, ModalContent, ModalHeader, ModalBody, ModalFooter } from '@heroui/react';
import { useAuth } from '../../../auth/AuthContext';
import { createReview, getAllReviews, updateReview, deleteReview } from '../../../service/reviewService';
import { Review, CreateReviewData, ReviewData } from '../../../types/review';
import ReviewForm from '../../../components/ReviewForm/ReviewForm';
import ReviewImages from '../../../components/ReviewImages/ReviewImages';
import BreadCrumb from '../../../components/BreadCrumb/BreadCrumb';

// Notification types
type NotificationType = 'success' | 'error' | 'warning' | 'info';

interface Notification {
  id: string;
  type: NotificationType;
  message: string;
  duration: number;
}

const Reviews = () => {
  const auth = useAuth();

  // Add custom CSS animations
  React.useEffect(() => {
    const style = document.createElement('style');
    style.textContent = `
      @keyframes fadeInUp {
        from {
          opacity: 0;
          transform: translateY(20px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }
      
      @keyframes starGlow {
        0%, 100% {
          filter: drop-shadow(0 0 2px rgba(251, 191, 36, 0.5));
        }
        50% {
          filter: drop-shadow(0 0 8px rgba(251, 191, 36, 0.8));
        }
      }
      
      @keyframes shimmer {
        0% {
          background-position: -200px 0;
        }
        100% {
          background-position: calc(200px + 100%) 0;
        }
      }
      
      .review-card {
        background: linear-gradient(
          90deg,
          transparent,
          rgba(255, 255, 255, 0.4),
          transparent
        );
        background-size: 200px 100%;
        background-repeat: no-repeat;
        background-position: -200px 0;
      }
      
      .review-card:hover {
        animation: shimmer 1.5s ease-in-out;
      }
    `;
    document.head.appendChild(style);
    
    return () => {
      document.head.removeChild(style);
    };
  }, []);

  // State management
  const [reviews, setReviews] = useState<Review[]>([]);
  const [loading, setLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalReviews, setTotalReviews] = useState(0);
  const [showReviewForm, setShowReviewForm] = useState(false);
  const [selectedReview, setSelectedReview] = useState<Review | null>(null);
  const [isEditMode, setIsEditMode] = useState(false);
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [filterRating, setFilterRating] = useState<string>('all');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [activeTab, setActiveTab] = useState<string>('show-reviews');
  const [reviewsPerPage, setReviewsPerPage] = useState(3);
  
  // View modal state
  const [showViewModal, setShowViewModal] = useState(false);
  const [selectedViewReview, setSelectedViewReview] = useState<Review | null>(null);

  // Function to add a notification
  const addNotification = (type: NotificationType, message: string, duration = 3000) => {
    const id = `notification-${Date.now()}`;
    const newNotification: Notification = { type, message, id, duration };
    setNotifications(prev => [...prev, newNotification]);

    // Auto-remove notification after duration
    setTimeout(() => {
      setNotifications(prev => prev.filter(notification => notification.id !== id));
    }, duration);
  };

  // Function to handle viewing a review
  const handleViewReview = (review: Review) => {
    setSelectedViewReview(review);
    setShowViewModal(true);
  };

  // Function to close view modal
  const handleCloseViewModal = () => {
    setShowViewModal(false);
    setSelectedViewReview(null);
  };

  // Function to handle reviews per page change
  const handleReviewsPerPageChange = (newPerPage: number) => {
    setReviewsPerPage(newPerPage);
    setCurrentPage(1);
    fetchReviews(1);
  };

  // Fetch reviews function
  const fetchReviews = useCallback(async (page = 1) => {
    try {
      setLoading(true);
      const response = await getAllReviews({
        page,
        limit: reviewsPerPage
      });

      if (response && response.reviews) {
        setReviews(response.reviews);
        setCurrentPage(response.currentPage || page);
        setTotalPages(response.totalPages || 1);
        setTotalReviews(response.totalReviews || response.reviews.length);
      } else {
        setReviews([]);
        setCurrentPage(1);
        setTotalPages(1);
        setTotalReviews(0);
      }
    } catch (error) {
      console.error('Error fetching reviews:', error);
      addNotification('error', 'Failed to fetch reviews. Please try again.');
      setReviews([]);
    } finally {
      setLoading(false);
    }
  }, [reviewsPerPage]);

  // Initial load
  useEffect(() => {
    fetchReviews(1);
  }, [fetchReviews]);

  // Change page - now fetches new data from API
  const handlePageChange = useCallback((page: number) => {
    if (page !== currentPage && page >= 1 && page <= totalPages) {
      setCurrentPage(page);
      fetchReviews(page);
      window.scrollTo({ top: 0, behavior: 'smooth' });
    }
  }, [currentPage, totalPages, fetchReviews]);

  // Pagination helper functions
  const goToFirstPage = useCallback(() => handlePageChange(1), [handlePageChange]);
  const goToLastPage = useCallback(() => handlePageChange(totalPages), [handlePageChange, totalPages]);
  const goToPreviousPage = useCallback(() => handlePageChange(currentPage - 1), [handlePageChange, currentPage]);
  const goToNextPage = useCallback(() => handlePageChange(currentPage + 1), [handlePageChange, currentPage]);

  // Generate page numbers for pagination
  const getPageNumbers = () => {
    const pages = [];
    const maxVisiblePages = 5;
    
    if (totalPages <= maxVisiblePages) {
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      const halfVisible = Math.floor(maxVisiblePages / 2);
      let startPage = Math.max(1, currentPage - halfVisible);
      let endPage = Math.min(totalPages, currentPage + halfVisible);
      
      if (currentPage <= halfVisible) {
        endPage = Math.min(totalPages, maxVisiblePages);
      } else if (currentPage > totalPages - halfVisible) {
        startPage = Math.max(1, totalPages - maxVisiblePages + 1);
      }
      
      if (startPage > 1) {
        pages.push(1);
        if (startPage > 2) {
          pages.push('...');
        }
      }
      
      for (let i = startPage; i <= endPage; i++) {
        pages.push(i);
      }
      
      if (endPage < totalPages) {
        if (endPage < totalPages - 1) {
          pages.push('...');
        }
        pages.push(totalPages);
      }
    }
    
    return pages;
  };

  // Keyboard navigation for pagination
  useEffect(() => {
    const handleKeyPress = (event: KeyboardEvent) => {
      if (event.target instanceof HTMLInputElement || event.target instanceof HTMLTextAreaElement || event.target instanceof HTMLSelectElement) {
        return;
      }
      
      switch (event.key) {
        case 'ArrowLeft':
          if (currentPage > 1) {
            event.preventDefault();
            goToPreviousPage();
          }
          break;
        case 'ArrowRight':
          if (currentPage < totalPages) {
            event.preventDefault();
            goToNextPage();
          }
          break;
        case 'Home':
          if (totalPages > 1) {
            event.preventDefault();
            goToFirstPage();
          }
          break;
        case 'End':
          if (totalPages > 1) {
            event.preventDefault();
            goToLastPage();
          }
          break;
      }
    };

    document.addEventListener('keydown', handleKeyPress);
    return () => document.removeEventListener('keydown', handleKeyPress);
  }, [currentPage, totalPages, goToPreviousPage, goToNextPage, goToFirstPage, goToLastPage]);

  return (
    <div className="mx-auto p-6 bg-gray-50 min-h-screen relative">
      {/* Notifications container */}
      <div className="fixed top-4 right-4 z-50 w-80 max-w-full">
        {notifications.map(notification => (
          <div key={notification.id} className="mb-3">
            Notification: {notification.message}
          </div>
        ))}
      </div>

      <BreadCrumb title="Reviews Dashboard" item1="Customer" />
      
      {/* Dashboard Header */}
      <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center mb-8 gap-4">
        <div>
          <h2 className="text-3xl font-bold text-gray-800 mb-2">Reviews Dashboard</h2>
          <p className="text-gray-600">Manage and view all your service reviews</p>
        </div>
      </div>

      {/* Content will be added in next edit */}
      <div className="text-center py-8">
        <p>Reviews content will be loaded here...</p>
      </div>
    </div>
  );
};

export default Reviews;
