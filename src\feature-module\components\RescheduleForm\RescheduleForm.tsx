import { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Divider
} from '@heroui/react';
import { FaCalendarAlt, FaClock, FaInfoCircle, FaCheckCircle } from 'react-icons/fa';
import { IoMdAlert } from 'react-icons/io';
import CustomButton from '../CustomButton';
import Calendar from 'react-calendar';
import 'react-calendar/dist/Calendar.css';

interface RescheduleFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (rescheduleData: RescheduleData) => void;
  bookingDetails: {
    id: number;
    service: string;
    date: string;
    provider: string;
  };
}

export interface RescheduleData {
  bookingId: number;
  newDate: Date;
  newTime: string;
}

const RescheduleForm = ({
  isOpen,
  onClose,
  onSubmit,
  bookingDetails
}: RescheduleFormProps) => {
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [selectedTime, setSelectedTime] = useState<string>('');
  const [availableTimes, setAvailableTimes] = useState<TimeSlot[]>([]);
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [showSuccess, setShowSuccess] = useState<boolean>(false);
  const [errors, setErrors] = useState<{
    date?: string;
    time?: string;
  }>({});

  // Parse the original booking date for display
  const originalDate = new Date(bookingDetails.date.split(',')[0]);
  const originalTime = bookingDetails.date.split(',')[1]?.trim() || '';

  useEffect(() => {
    // Set the default selected date to the current booking date
    setSelectedDate(originalDate);

    // Generate available time slots
    generateTimeSlots(originalDate);
  }, [bookingDetails]);

  // Interface for time slot data
  interface TimeSlot {
    time: string;
    available: boolean;
    reason?: string;
    popularity?: 'low' | 'medium' | 'high';
  }

  // Generate time slots for the selected date
  const generateTimeSlots = (date: Date) => {
    setIsLoading(true);

    // Simulate API call delay
    setTimeout(() => {
      // In a real app, you would fetch available time slots from the server
      // For demo purposes, we'll generate some time slots with availability status
      const timeSlots: TimeSlot[] = [];
      const currentDate = new Date();

      // Start times from 9 AM to 5 PM with 30-minute intervals for more options
      for (let hour = 9; hour <= 17; hour++) {
        for (let minute of [0, 30]) {
          // Skip if we're at 5:30 PM
          if (hour === 17 && minute === 30) continue;

          const formattedHour = hour % 12 === 0 ? 12 : hour % 12;
          const period = hour < 12 ? 'AM' : 'PM';
          const startTime = `${formattedHour}:${minute === 0 ? '00' : minute} ${period}`;

          // Calculate end time (30 minutes later)
          let endHour = minute === 30 ? hour + 1 : hour;
          let endMinute = minute === 30 ? 0 : 30;
          const formattedEndHour = endHour % 12 === 0 ? 12 : endHour % 12;
          const endPeriod = endHour < 12 ? 'AM' : 'PM';
          const endTime = `${formattedEndHour}:${endMinute === 0 ? '00' : endMinute} ${endPeriod}`;

          const timeString = `${startTime}-${endTime}`;

          // Determine availability based on various conditions
          let available = true;
          let reason = '';
          let popularity: 'low' | 'medium' | 'high' = 'medium';

          // If the selected date is today, only show future time slots
          if (date.toDateString() === currentDate.toDateString() &&
              (hour < currentDate.getHours() ||
               (hour === currentDate.getHours() && minute <= currentDate.getMinutes()))) {
            available = false;
            reason = 'Past time';
          }

          // Simulate some time slots being booked (for demo purposes)
          // Make some specific times unavailable based on the day of week
          const dayOfWeek = date.getDay();

          // Morning slots tend to be booked on Monday and Wednesday
          if ((dayOfWeek === 1 || dayOfWeek === 3) && hour < 12) {
            // Higher chance of being booked in prime hours (10-11:30)
            if (hour >= 10 && hour < 12 && Math.random() > 0.2) {
              available = false;
              reason = 'Fully booked';
            } else if (Math.random() > 0.5) {
              available = false;
              reason = 'Fully booked';
            }
            popularity = 'high';
          }

          // Afternoon slots tend to be booked on Tuesday and Thursday
          if ((dayOfWeek === 2 || dayOfWeek === 4) && hour >= 13) {
            if (hour >= 14 && hour <= 16 && Math.random() > 0.3) {
              available = false;
              reason = 'Fully booked';
            } else if (Math.random() > 0.6) {
              available = false;
              reason = 'Fully booked';
            }
            popularity = 'high';
          }

          // Friday afternoons are popular and often booked
          if (dayOfWeek === 5 && hour >= 14) {
            if (Math.random() > 0.2) {
              available = false;
              reason = 'Fully booked';
            }
            popularity = 'high';
          }

          // Lunch hour is often unavailable
          if (hour === 12 || (hour === 13 && minute === 0)) {
            if (Math.random() > 0.7) { // 30% chance of being available
              available = false;
              reason = 'Lunch hour';
            }
            popularity = 'medium';
          }

          // Early morning and late afternoon usually have low popularity
          if ((hour === 9 || (hour === 17 && minute === 0)) && available) {
            popularity = 'low';
          }

          timeSlots.push({
            time: timeString,
            available,
            reason: available ? '' : reason,
            popularity: available ? popularity : undefined
          });
        }
      }

      setAvailableTimes(timeSlots);
      setIsLoading(false);

      // Clear selected time when date changes
      setSelectedTime('');
    }, 500); // Simulate a 500ms delay for API call
  };

  const handleDateChange = (date: Date | Date[] | null) => {
    if (date instanceof Date) {
      setSelectedDate(date);
      generateTimeSlots(date);
    }
  };

  const handleTimeSelect = (timeSlot: TimeSlot) => {
    if (timeSlot.available) {
      setSelectedTime(timeSlot.time);
    }
  };

  const validateForm = () => {
    const newErrors: {
      date?: string;
      time?: string;
    } = {};

    if (!selectedDate) {
      newErrors.date = 'Please select a date';
    }

    if (!selectedTime) {
      newErrors.time = 'Please select a time slot';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    setIsSubmitting(true);

    try {
      const rescheduleData: RescheduleData = {
        bookingId: bookingDetails.id,
        newDate: selectedDate as Date,
        newTime: selectedTime,
      };

      // Show success message before closing
      setShowSuccess(true);

      // Wait for animation to complete before submitting
      setTimeout(async () => {
        await onSubmit(rescheduleData);

        // Wait a moment for the success message to be visible
        setTimeout(() => {
          onClose();
          setShowSuccess(false);
        }, 1500);
      }, 500);
    } catch (error) {
      console.error('Error rescheduling booking:', error);
      setIsSubmitting(false);
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="xl" className="reschedule-modal">
      <ModalContent>
        {showSuccess && (
          <div className="absolute inset-0 bg-white bg-opacity-90 z-10 flex flex-col items-center justify-center rounded-lg">
            <FaCheckCircle className="text-green-500 text-5xl mb-4 animate-bounce" />
            <h3 className="text-xl font-semibold text-green-700 mb-2">Booking Rescheduled!</h3>
            <p className="text-green-600">Your appointment has been successfully rescheduled.</p>
          </div>
        )}

        <ModalHeader className="flex flex-col gap-1 border-b pb-3">
          <h2 className="text-xl">Reschedule Booking</h2>
        </ModalHeader>

        <ModalBody>
          <div className="space-y-4">
            {/* Compact current booking details */}
            <div className="bg-blue-50 p-3 rounded-md text-sm">
              <div className="flex items-center justify-between">
                <h3 className="font-semibold text-blue-800">Current Booking</h3>
                <Chip color="primary" variant="flat" size="sm">{bookingDetails.service}</Chip>
              </div>
              <Divider className="my-2" />
              <div className="grid grid-cols-2 gap-2">
                <p className="text-blue-700"><strong>Provider:</strong> {bookingDetails.provider}</p>
                <p className="text-blue-700"><strong>Date & Time:</strong> {bookingDetails.date}</p>
              </div>
            </div>

            {/* New booking summary - only show when both date and time are selected */}
            {selectedDate && selectedTime && (
              <div className="bg-green-50 p-3 rounded-md border border-green-200 text-sm">
                <div className="flex items-center justify-between">
                  <h3 className="font-semibold text-green-800">New Booking Details</h3>
                  <Chip color="success" variant="flat" size="sm">Ready to confirm</Chip>
                </div>
                <Divider className="my-2" />
                <div className="grid grid-cols-2 gap-2">
                  <p className="text-green-700">
                    <strong>New Date:</strong> {selectedDate.toLocaleDateString('en-US', {
                      weekday: 'short',
                      month: 'short',
                      day: 'numeric'
                    })}
                  </p>
                  <p className="text-green-700">
                    <strong>New Time:</strong> {selectedTime}
                  </p>
                </div>
              </div>
            )}

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Date selection */}
              <div>
                <h3 className="text-md font-semibold mb-2 flex items-center">
                  <FaCalendarAlt className="mr-2 text-gray-600" />
                  Select New Date
                </h3>
                <div className="border rounded-md p-2 reschedule-calendar-container">
                  <Calendar
                    onChange={handleDateChange}
                    value={selectedDate}
                    minDate={new Date()}
                    className="w-full border-0 mx-auto text-sm"
                  />
                </div>
                {errors.date && (
                  <p className="mt-1 text-xs text-red-600">{errors.date}</p>
                )}
              </div>

              {/* Time selection */}
              <div>
                <h3 className="text-md font-semibold mb-2 flex items-center">
                  <FaClock className="mr-2 text-gray-600" />
                  Select New Time
                </h3>
                <div className="border rounded-md p-3 h-full">
                  {/* Time slot legend */}
                  <div className="flex flex-wrap gap-2 mb-3 justify-start text-xs">
                    <Tooltip content="Available time slots">
                      <div className="flex items-center">
                        <div className="w-3 h-3 border border-gray-300 bg-white rounded-sm mr-1"></div>
                        <span>Available</span>
                      </div>
                    </Tooltip>
                    <Tooltip content="Your selected time">
                      <div className="flex items-center">
                        <div className="w-3 h-3 bg-blue-500 rounded-sm mr-1"></div>
                        <span>Selected</span>
                      </div>
                    </Tooltip>
                    <Tooltip content="These slots are already booked">
                      <div className="flex items-center">
                        <div className="w-3 h-3 bg-red-100 border border-red-300 rounded-sm mr-1"></div>
                        <span>Unavailable</span>
                      </div>
                    </Tooltip>
                    <Tooltip content="Click for more information">
                      <div className="flex items-center text-blue-500 cursor-help">
                        <FaInfoCircle size={12} className="mr-1" />
                        <span>Help</span>
                      </div>
                    </Tooltip>
                  </div>

                  {/* Time slots grid */}
                  <div className="grid grid-cols-2 gap-2 max-h-[280px] overflow-y-auto pr-1">
                    {isLoading ? (
                      <div className="col-span-2 flex justify-center items-center py-10">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                        <span className="ml-2 text-sm text-gray-600">Loading available times...</span>
                      </div>
                    ) : availableTimes.length > 0 ? (
                      <>
                        {availableTimes.some(slot => slot.available) ? (
                          availableTimes.map((timeSlot, index) => (
                            <Tooltip
                              key={index}
                              content={
                                timeSlot.available
                                  ? timeSlot.popularity === 'high'
                                    ? "Popular time - book quickly!"
                                    : timeSlot.popularity === 'low'
                                      ? "Quiet time - good availability"
                                      : "Standard availability"
                                  : `Unavailable: ${timeSlot.reason}`
                              }
                              placement="bottom"
                            >
                              <div className="relative">
                                <Button
                                  color={selectedTime === timeSlot.time ? 'primary' : timeSlot.available ? 'default' : 'danger'}
                                  variant={selectedTime === timeSlot.time ? 'solid' : timeSlot.available ? 'bordered' : 'flat'}
                                  onPress={() => handleTimeSelect(timeSlot)}
                                  radius="sm"
                                  size="sm"
                                  isDisabled={!timeSlot.available}
                                  className={`w-full ${!timeSlot.available ? 'opacity-70' : ''}`}
                                >
                                  <div className="flex items-center justify-between w-full">
                                    <span className="text-xs">{timeSlot.time}</span>
                                    {!timeSlot.available && (
                                      <IoMdAlert className="text-red-500" size={14} />
                                    )}
                                    {timeSlot.available && timeSlot.popularity === 'high' && (
                                      <span className="w-2 h-2 bg-orange-400 rounded-full"></span>
                                    )}
                                  </div>
                                </Button>
                              </div>
                            </Tooltip>
                          ))
                        ) : (
                          <div className="col-span-2 bg-red-50 border border-red-200 rounded-md p-3 text-center">
                            <p className="text-red-500 font-medium text-sm mb-1">
                              No available time slots for this date
                            </p>
                            <p className="text-red-400 text-xs">
                              Please select another date from the calendar
                            </p>
                          </div>
                        )}
                      </>
                    ) : (
                      <p className="col-span-2 text-gray-500 text-center py-4 text-sm">
                        Please select a date to view available times
                      </p>
                    )}
                  </div>
                </div>
                {errors.time && (
                  <p className="mt-1 text-xs text-red-600">{errors.time}</p>
                )}
              </div>
            </div>
          </div>
        </ModalBody>

        <ModalFooter className="border-t pt-3">
          <Button color="danger" variant="light" onPress={onClose} size="sm">
            Cancel
          </Button>
          <CustomButton
            label={isSubmitting ? 'Submitting...' : 'Confirm Reschedule'}
            color="primary"
            size="sm"
            isLoading={isSubmitting}
            isDisabled={isSubmitting || !selectedDate || !selectedTime}
            onPress={handleSubmit}
          />
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default RescheduleForm;
