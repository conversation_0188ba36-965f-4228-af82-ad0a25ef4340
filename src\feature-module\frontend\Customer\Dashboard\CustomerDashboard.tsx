import {
  FaShoppingCart,
  FaWallet,
  FaPiggyBank,
  FaMoneyBillWave,
  FaEye,
  FaUser,
} from 'react-icons/fa';
import BreadCrumb from '../../../components/common/breadcrumb/breadCrumb';
import CustomButton from '../../../components/CustomButton';
import { Chip } from '@heroui/react';

export default function Dashboard() {
  // Statistics Data
  const stats = [
    {
      title: 'Total Orders',
      value: '27',
      percentage: '16%',
      color: 'text-pink-500',
      bg: 'bg-pink-100',
      icon: <FaShoppingCart />,
    },
    {
      title: 'Total Spend',
      value: '$2,500',
      percentage: '5%',
      color: 'text-red-500',
      bg: 'bg-red-100',
      icon: <FaMoneyBillWave />,
    },
    {
      title: 'Wallet',
      value: '$200',
      percentage: '5%',
      color: 'text-red-500',
      bg: 'bg-red-100',
      icon: <FaWallet />,
    },
    {
      title: 'Total Savings',
      value: '$354',
      percentage: '16%',
      color: 'text-green-500',
      bg: 'bg-green-100',
      icon: <FaPiggyBank />,
    },
  ];

  // Transactions Data
  const transactions = [
    {
      type: 'Service Booking',
      date: '02 Sep 2022, 09:12 AM',
      amount: '$280.00',
      status: 'In Process',
    },
    {
      type: 'Service Refund',
      date: '02 Sep 2022, 04:36 PM',
      amount: '$395.00',
      status: 'Completed',
    },
    {
      type: 'Wallet Topup',
      date: '01 Sep 2022, 10:00 AM',
      amount: '$1000.00',
      status: 'Pending',
    },
    {
      type: 'Service Booking',
      date: '31 Aug 2022, 11:17 AM',
      amount: '$598.65',
      status: 'Cancelled',
    },
    {
      type: 'Service Booking',
      date: '10 Nov 2022',
      amount: '$300.00',
      status: 'Completed',
    },
  ];

  // Bookings Data
  const bookings = [
    {
      service: 'Computer Repair',
      date: '10 Nov 2022',
      name: 'John Smith',
      email: '<EMAIL>',
      status: 'In Process',
    },
    {
      service: 'Car Repair',
      date: '15 Oct 2022',
      name: 'Timothy',
      email: '<EMAIL>',
      status: 'Pending',
    },
    {
      service: 'Interior Designing',
      date: '18 Oct 2022',
      name: 'Jordan',
      email: '<EMAIL>',
      status: 'Completed',
    },
    {
      service: 'Steam Car Wash',
      date: '28 Oct 2022',
      name: 'Armand',
      email: '<EMAIL>',
      status: 'Cancelled',
    },
    {
      service: 'House Cleaning',
      date: '10 Nov 2022',
      name: 'Joseph',
      email: '<EMAIL>',
      status: 'Completed',
    },
    {
      service: 'Car Repair',
      date: '10 Nov 2022',
      name: 'Adrian',
      email: '<EMAIL>',
      status: 'In Process',
    },
  ];

  // Get color classes for status
  const getStatusColor = (status: string) => {
    switch (status) {
      case "Completed":
        return "success";
      case "Pending":
        return "warning";
      case "In Process":
        return "primary";
      case "Cancelled":
        return "danger";
      default:
        return "default";
    }
  };

  // StatusChip component for consistent status display
  const StatusChip = ({ status }: { status: string }) => (
    <Chip
      color={getStatusColor(status) as any}
      variant="flat"
      size="sm"
      radius="sm"
      className="font-medium"
    >
      {status}
    </Chip>
  );

  return (
    <div className="max-w-screen-xl mx-auto p-3 sm:p-4 md:p-6 w-full">
      {/* Breadcrumb & Title */}
      <BreadCrumb title="Dashboard" item1="Customer" />
      <h2 className="text-2xl font-semibold text-gray-800 mt-4 mb-6">Dashboard</h2>

      {/* Stats Cards Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6 mb-8">
        {stats.map((stat, index) => (
          <div
            key={index}
            className="bg-white p-4 shadow-md hover:shadow-lg transition-shadow duration-300 rounded-lg flex items-center justify-between"
          >
            <div className={`p-3 rounded-full ${stat.bg} text-lg flex items-center justify-center`}>
              {stat.icon}
            </div>
            <div className="flex-1 ml-4">
              <p className="text-gray-500 text-sm font-medium">{stat.title}</p>
              <p className="text-xl font-semibold">{stat.value}</p>
            </div>
            <span
              className={`px-3 py-1 text-xs font-semibold rounded-full ${stat.color} bg-opacity-20`}
            >
              {stat.percentage}
            </span>
          </div>
        ))}
      </div>

      {/* Transactions & Bookings Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Transactions */}
        <div className="bg-white p-5 md:p-6 shadow-md hover:shadow-lg transition-shadow duration-300 rounded-lg">
          <div className="flex justify-between items-center mb-5">
            <h3 className="font-bold text-lg text-gray-800">Recent Transactions</h3>
            <CustomButton
              label="View All"
              color="primary"
              variant="light"
              size="sm"
              endContent={<FaEye />}
              onPress={() => console.log('View all transactions')}
            />
          </div>
          <div className="space-y-3">
            {transactions.slice(0, 4).map((transaction, index) => (
              <div
                key={index}
                className="flex justify-between items-center bg-gray-50 hover:bg-gray-100 transition-colors duration-200 p-4 rounded-lg border border-gray-100"
              >
                <div className="flex-1">
                  <h4 className="text-base md:text-lg font-medium">{transaction.type}</h4>
                  <p className="text-sm text-gray-500">
                    {transaction.date}
                  </p>
                </div>
                <div className="flex items-center gap-3">
                  <StatusChip status={transaction.status} />
                  <span className="font-semibold text-gray-700 text-lg">
                    {transaction.amount}
                  </span>
                </div>
              </div>
            ))}
          </div>
          {transactions.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              <p>No recent transactions found</p>
            </div>
          )}
        </div>

        {/* Recent Bookings */}
        <div className="bg-white p-5 md:p-6 shadow-md hover:shadow-lg transition-shadow duration-300 rounded-lg">
          <div className="flex justify-between items-center mb-5">
            <h3 className="text-lg font-bold text-gray-800">Recent Bookings</h3>
            <CustomButton
              label="View All"
              color="primary"
              variant="light"
              size="sm"
              endContent={<FaEye />}
              onPress={() => console.log('View all bookings')}
            />
          </div>
          <div className="space-y-3">
            {bookings.slice(0, 4).map((booking, index) => (
              <div
                key={index}
                className="flex flex-col sm:flex-row items-start sm:items-center justify-between bg-gray-50 hover:bg-gray-100 transition-colors duration-200 p-4 rounded-lg border border-gray-100"
              >
                <div className="flex items-center space-x-3 md:space-x-4 flex-1">
                  <div className="w-14 h-14 sm:w-16 sm:h-16 bg-gradient-to-br from-blue-100 to-blue-200 rounded-lg flex items-center justify-center">
                    <FaUser className="text-blue-600 text-xl" />
                  </div>
                  <div className="flex-1">
                    <h4 className="text-base md:text-lg font-medium">{booking.service}</h4>
                    <p className="text-sm text-gray-500">
                      {booking.date}
                    </p>
                    <p className="text-sm text-gray-600 font-medium">{booking.name}</p>
                    <p className="text-xs text-gray-500">{booking.email}</p>
                  </div>
                </div>
                <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3 w-full sm:w-auto mt-3 sm:mt-0">
                  <StatusChip status={booking.status} />
                  {booking.status !== 'Completed' && booking.status !== 'Cancelled' && (
                    <CustomButton
                      color="danger"
                      label="Cancel"
                      size="sm"
                      variant="light"
                      onPress={() => console.log(`Cancel booking for ${booking.name}`)}
                    />
                  )}
                </div>
              </div>
            ))}
          </div>
          {bookings.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              <p>No recent bookings found</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
