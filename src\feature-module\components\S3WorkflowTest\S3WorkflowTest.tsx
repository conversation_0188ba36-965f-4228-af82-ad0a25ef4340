import React, { useState } from 'react';
import { <PERSON><PERSON>, Card, CardBody, Progress } from '@heroui/react';
import { FiUpload, FiCheck, FiX, FiImage } from 'react-icons/fi';
import { 
  uploadToS3WithImageName, 
  getImageUrlFromName, 
  getImageUrlsFromNames,
  deleteFromS3ByImageName 
} from '../../frontend/Customer/aws/s3FileUpload';
import ReviewImages from '../ReviewImages/ReviewImages';

interface TestResult {
  step: string;
  status: 'pending' | 'success' | 'error';
  message: string;
  data?: any;
}

const S3WorkflowTest: React.FC = () => {
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [isRunning, setIsRunning] = useState(false);
  const [imageNames, setImageNames] = useState<string[]>([]);
  const [imageUrls, setImageUrls] = useState<string[]>([]);

  const addResult = (step: string, status: 'success' | 'error', message: string, data?: any) => {
    setTestResults(prev => [...prev, { step, status, message, data }]);
  };

  const runWorkflowTest = async () => {
    setIsRunning(true);
    setTestResults([]);
    setImageNames([]);
    setImageUrls([]);

    try {
      // Step 1: Test S3 Upload Function
      addResult('S3 Upload', 'pending', 'Testing S3 upload function...');
      
      // Create a test file
      const testFile = new File(['test content'], 'test-image.jpg', { type: 'image/jpeg' });
      
      const uploadResult = await uploadToS3WithImageName(testFile, 'review-images');
      
      if (uploadResult.imageName && uploadResult.fullUrl) {
        addResult('S3 Upload', 'success', `Image uploaded successfully: ${uploadResult.imageName}`, uploadResult);
        setImageNames([uploadResult.imageName]);
        setImageUrls([uploadResult.fullUrl]);
      } else {
        addResult('S3 Upload', 'error', 'Upload result missing required fields');
        return;
      }

      // Step 2: Test URL Reconstruction
      addResult('URL Reconstruction', 'pending', 'Testing URL reconstruction from image name...');
      
      const reconstructedUrl = getImageUrlFromName(uploadResult.imageName, 'review-images');
      
      if (reconstructedUrl === uploadResult.fullUrl) {
        addResult('URL Reconstruction', 'success', 'URL reconstruction successful');
      } else {
        addResult('URL Reconstruction', 'error', `URL mismatch: ${reconstructedUrl} !== ${uploadResult.fullUrl}`);
      }

      // Step 3: Test Multiple URL Reconstruction
      addResult('Multiple URLs', 'pending', 'Testing multiple URL reconstruction...');
      
      const multipleUrls = getImageUrlsFromNames([uploadResult.imageName], 'review-images');
      
      if (multipleUrls.length === 1 && multipleUrls[0] === uploadResult.fullUrl) {
        addResult('Multiple URLs', 'success', 'Multiple URL reconstruction successful');
      } else {
        addResult('Multiple URLs', 'error', 'Multiple URL reconstruction failed');
      }

      // Step 4: Test Review Payload Structure
      addResult('Review Payload', 'pending', 'Testing review payload structure...');
      
      const reviewPayload = {
        providerId: 'test-provider',
        serviceId: 'test-service',
        bookingId: 'test-booking',
        rating: 4,
        title: 'Test Review',
        comment: 'This is a test review with images',
        imageNames: [uploadResult.imageName],
        date: new Date().toISOString()
      };

      addResult('Review Payload', 'success', 'Review payload created successfully', reviewPayload);

      // Step 5: Test Image Display Component
      addResult('Image Display', 'pending', 'Testing image display component...');
      
      // The ReviewImages component should render correctly with imageNames
      addResult('Image Display', 'success', 'Image display component ready');

      // Step 6: Test Cleanup (Optional - commented out to avoid deleting test image)
      // addResult('S3 Cleanup', 'pending', 'Testing S3 cleanup...');
      // await deleteFromS3ByImageName(uploadResult.imageName, 'review-images');
      // addResult('S3 Cleanup', 'success', 'S3 cleanup successful');

    } catch (error) {
      addResult('Error', 'error', `Test failed: ${error instanceof Error ? error.message : String(error)}`);
    } finally {
      setIsRunning(false);
    }
  };

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'success':
        return <FiCheck className="text-green-500" />;
      case 'error':
        return <FiX className="text-red-500" />;
      default:
        return <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />;
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <Card>
        <CardBody>
          <h2 className="text-2xl font-bold mb-4 flex items-center gap-2">
            <FiImage className="text-blue-500" />
            S3 Image Upload Workflow Test
          </h2>
          
          <p className="text-gray-600 mb-6">
            This test verifies the complete S3 image upload workflow for the review system.
            It tests image upload, URL reconstruction, payload creation, and display components.
          </p>

          <Button
            color="primary"
            size="lg"
            startContent={<FiUpload />}
            onPress={runWorkflowTest}
            isLoading={isRunning}
            disabled={isRunning}
            className="mb-6"
          >
            {isRunning ? 'Running Tests...' : 'Run Workflow Test'}
          </Button>

          {isRunning && (
            <Progress
              size="sm"
              isIndeterminate
              color="primary"
              className="mb-4"
              label="Testing S3 workflow..."
            />
          )}
        </CardBody>
      </Card>

      {/* Test Results */}
      {testResults.length > 0 && (
        <Card>
          <CardBody>
            <h3 className="text-xl font-semibold mb-4">Test Results</h3>
            <div className="space-y-3">
              {testResults.map((result, index) => (
                <div key={index} className="flex items-start gap-3 p-3 rounded-lg bg-gray-50">
                  <div className="mt-0.5">
                    {getStatusIcon(result.status)}
                  </div>
                  <div className="flex-1">
                    <div className="font-medium">{result.step}</div>
                    <div className="text-sm text-gray-600">{result.message}</div>
                    {result.data && (
                      <details className="mt-2">
                        <summary className="text-xs text-blue-600 cursor-pointer">View Data</summary>
                        <pre className="text-xs bg-gray-100 p-2 rounded mt-1 overflow-auto">
                          {JSON.stringify(result.data, null, 2)}
                        </pre>
                      </details>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </CardBody>
        </Card>
      )}

      {/* Image Display Test */}
      {imageNames.length > 0 && (
        <Card>
          <CardBody>
            <h3 className="text-xl font-semibold mb-4">Image Display Test</h3>
            <p className="text-gray-600 mb-4">
              Testing the ReviewImages component with uploaded image names:
            </p>
            
            <div className="space-y-4">
              <div>
                <h4 className="font-medium mb-2">Using Image Names (Primary Method):</h4>
                <ReviewImages
                  imageNames={imageNames}
                  imageUrls={[]}
                  maxDisplay={3}
                  size="lg"
                  showCount={true}
                  folderName="review-images"
                />
              </div>
              
              <div>
                <h4 className="font-medium mb-2">Using Image URLs (Fallback Method):</h4>
                <ReviewImages
                  imageNames={[]}
                  imageUrls={imageUrls}
                  maxDisplay={3}
                  size="lg"
                  showCount={true}
                  folderName="review-images"
                />
              </div>
            </div>

            <div className="mt-4 p-3 bg-blue-50 rounded-lg">
              <h4 className="font-medium text-blue-800 mb-2">Test Data:</h4>
              <div className="text-sm text-blue-700">
                <div><strong>Image Names:</strong> {JSON.stringify(imageNames)}</div>
                <div><strong>Image URLs:</strong> {JSON.stringify(imageUrls)}</div>
              </div>
            </div>
          </CardBody>
        </Card>
      )}
    </div>
  );
};

export default S3WorkflowTest;
