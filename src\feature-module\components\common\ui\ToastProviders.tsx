// import { Hero<PERSON><PERSON>rovider, ToastProvider } from '@heroui/react';

// import { ReactNode } from 'react';

// const ToastProviders = ({ children }: { children: ReactNode }) => {
//   return (
//     <>
//       <HeroUIProvider>
//         <ToastProvider
//           placement="top-center"
//           maxVisibleToasts={3}
//           toastProps={{
//             radius: 'md',
//             color: 'primary',
//             variant: 'flat',
//             timeout: 3000,
//             hideIcon: false,
//             classNames: {
//               closeButton:
//                 'opacity-100 absolute right-4 top-1/2 -translate-y-1/2',
//             },
//           }}
//         />
//         {children}
//       </HeroUIProvider>
//     </>
//   );
// };

// export default ToastProviders;
