/* Animation for notifications */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
    transform: translateY(0);
  }
  to {
    opacity: 0;
    transform: translateY(-10px);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

.animate-fadeIn {
  animation: fadeIn 0.3s ease-in-out forwards;
}

.animate-fadeOut {
  animation: fadeOut 0.3s ease-in-out forwards;
}

.animate-slideInUp {
  animation: slideInUp 0.4s ease-out forwards;
}

.animate-pulse {
  animation: pulse 2s infinite;
}

/* Enhanced hover effects for review cards */
.review-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.review-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.review-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.review-card:hover::before {
  left: 100%;
}

/* Star rating animations */
.star-rating {
  display: flex;
  gap: 2px;
}

.star-rating svg {
  transition: all 0.2s ease-in-out;
}

.star-rating svg:hover {
  transform: scale(1.1);
}

/* Text truncation utility */
.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Dashboard card gradients */
.dashboard-card-blue {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.dashboard-card-green {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.dashboard-card-purple {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.dashboard-card-orange {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

/* Responsive grid improvements */
@media (max-width: 768px) {
  .review-card {
    margin-bottom: 1rem;
  }
}

/* Loading skeleton animation */
@keyframes skeleton {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: skeleton 1.5s infinite;
}

/* Custom scrollbar for review content */
.review-content::-webkit-scrollbar {
  width: 4px;
}

.review-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 2px;
}

.review-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 2px;
}

.review-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
