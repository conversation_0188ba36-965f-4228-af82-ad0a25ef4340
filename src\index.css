/* @import "tailwindcss"; */
@tailwind base;
@tailwind components;
@tailwind utilities;

.form-control {
  border-color: #dcdcdc;
  color: #333;
  font-size: 15px;
  min-height: 42px;
  padding: 6px 15px;
}

.form-label {
  font-size: 14px;
  font-weight: 500;
  color: #242b3a;
  margin-bottom: 0.5rem;
}

h4 {
  font-size: 24px;
  font-weight: 600;
}

h5 {
  font-size: 20px;
  font-weight: 600;
}



.input-group {
  position: relative;
  display: flex;
  flex-wrap: wrap;
  align-items: stretch;
  width: 100%;
}

.list-box {
  border-radius: 5px !important;
}

/* Calendar styling */
.react-calendar {
  border: none !important;
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);
  width: 100% !important;
  max-width: 100% !important;
  margin: 0 auto;
  font-family: inherit;
}

/* Remove dotted focus outline on days */
.react-calendar__tile:focus {
  outline: none !important;
  box-shadow: none !important;
}

/* Make the selected date round */
.react-calendar__tile--active {
  background: #6366f1 !important; /* Change to your preferred color */
  color: white !important;
  border-radius: 10% !important;
}

/* Hover effect for date tiles */
.react-calendar__tile:hover {
  background-color: #f3f4f6 !important;
  border-radius: 10% !important;
}

/* Remove dotted line under the day names */
.react-calendar__month-view__weekdays {
  text-transform: uppercase;
  font-weight: bold;
  border-bottom: none !important;
  text-align: center;
}

/* Center the day names */
.react-calendar__month-view__weekdays__weekday {
  text-align: center;
  padding: 0.5rem 0;
}

/* Center the day numbers */
.react-calendar__month-view__days__day {
  text-align: center;
  padding: 0.75rem 0;
}

/* Remove dotted focus effect when navigating */
.react-calendar__navigation button:focus {
  outline: none !important;
  box-shadow: none !important;
}

/* Style the navigation buttons */
.react-calendar__navigation {
  margin-bottom: 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* Style the month and year display */
.react-calendar__navigation__label {
  font-weight: bold;
  font-size: 1rem;
  color: #333;
}

/* Custom styles for the reschedule form */
.reschedule-calendar-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  background-color: #ffffff;
  border-radius: 0.375rem;
}

.reschedule-calendar {
  width: 100%;
  max-width: 400px;
  margin: 0 auto;
}

/* Time selection grid styles */
.reschedule-calendar .grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(90px, 1fr));
  gap: 0.75rem;
  width: 100%;
  margin-top: 0.5rem;
  padding: 0.5rem 0;
}

/* Time button styles */
.reschedule-calendar button {
  width: 100%;
  text-align: center;
  font-size: 0.875rem;
  padding: 0.5rem 0.25rem;
  border-radius: 0.25rem;
  transition: all 0.2s ease;
  height: auto;
  min-height: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Unavailable time slot styles */
.reschedule-calendar button:disabled {
  cursor: not-allowed;
  background-color: #f9f9f9;
  border-color: #e5e5e5;
}

/* Tooltip for unavailable slots */
.time-slot-tooltip {
  position: absolute;
  top: -30px;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  z-index: 10;
  white-space: nowrap;
  opacity: 0;
  transition: opacity 0.2s ease;
  pointer-events: none;
}

.time-slot-container:hover .time-slot-tooltip {
  opacity: 1;
}
@layer utilities {
  .text-heading1 {
    @apply text-[34px] font-bold tracking-[0.25px] leading-[1.2];
  }

  .text-heading2 {
    @apply text-[24px] font-normal tracking-[0px] leading-[1.3];
  }

  .text-heading3 {
    @apply text-[20px] font-medium tracking-[0.15px] leading-[1.4];
  }

  .text-subtitle1 {
    @apply text-[15px] font-semibold tracking-[0.0px] leading-[1.3] text-gray-800;
  }

  .text-subtitle2 {
    @apply text-[14px] font-normal tracking-[0.1px] leading-[1.5];
  }

  .text-subtitle3 {
    @apply text-[20px] font-semibold tracking-[0.15px] leading-[1.4];
  }

  .text-subtitle4 {
    @apply text-[16px] font-medium tracking-[0.15px] leading-[1.4];
  }

  .text-subtitle5 {
    @apply text-[14px] font-medium tracking-[0.15px] leading-[1.4];
  }

  .text-body1 {
    @apply text-[14px] font-normal tracking-[0.25px] leading-[1.6] opacity-90;
  }

  .text-body2 {
    @apply text-[16px] font-normal tracking-[0.5px] leading-[1.6];
  }

  .text-body3 {
    @apply text-[14px] font-normal tracking-[0.5px] leading-[1.6];
  }

  .text-navlink {
    @apply text-[14px] font-medium tracking-[0.25px] leading-[1.6] opacity-70;
  }

  .text-caption {
    @apply text-[12px] font-medium tracking-[0.2px] leading-[1.4] text-gray-600;
  }

  .text-caption2 {
    @apply text-[11px] font-medium tracking-[0.2px] leading-[1.4] text-gray-600;
  }

  .text-button {
    @apply text-[14px] font-normal capitalize tracking-[0.75px] leading-[1.5] dark:text-zinc-900 opacity-90;
  }

  .text-overline {
    @apply text-[10px] font-semibold uppercase tracking-[1.2px] leading-[1.5] opacity-50;
  }

  .text-error {
    @apply text-[12px] font-medium text-red-400;
  }
}