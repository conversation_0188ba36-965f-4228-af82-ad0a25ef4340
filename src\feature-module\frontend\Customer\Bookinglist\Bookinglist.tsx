import { useState, useCallback, useEffect } from 'react';
import { useAuth } from 'react-oidc-context';
import BreadCrumb from '../../../components/common/breadcrumb/breadCrumb';
import CustomButton from '../../../components/CustomButton';
import ReviewForm, { ReviewData } from '../../../../feature-module/components/ReviewForm/ReviewForm';
import RescheduleForm, { RescheduleData } from '../../../../feature-module/components/RescheduleForm/RescheduleForm';
import { useNavigate } from 'react-router-dom';
import { apiClient } from '../../../../api';
import { createReview, CreateReviewData } from '../../../../service/reviewService';
import { toast } from 'react-toastify';

// User data interface
interface UserData {
  name?: string;
  username?: string;
  email?: string;
  profileImage?: string;
}

const BookingList = () => {
  const auth = useAuth();
  const navigate = useNavigate();
  const [showReviewForm, setShowReviewForm] = useState(false);
  const [showRescheduleForm, setShowRescheduleForm] = useState(false);
  const [selectedBooking, setSelectedBooking] = useState<Booking | null>(null);
  const [userData, setUserData] = useState<UserData>({});
  const [userDataLoading, setUserDataLoading] = useState(false);

  // Get user ID from auth context
  const getUserId = useCallback(() => {
    if (auth.user) {
      return auth.user.profile.preferred_username ||
             auth.user.profile.sub ||
             auth.user.profile.email;
    }
    return null;
  }, [auth.user]);

  // Fetch user data from API
  const fetchUserData = useCallback(async () => {
    const uid = getUserId();
    if (!uid || !auth.isAuthenticated) {
      console.log('Cannot fetch user data: No user ID or not authenticated');
      return;
    }

    try {
      setUserDataLoading(true);
      console.log(`Fetching user data for ID: ${uid}`);

      const response = await apiClient.get(`/api/v1/user/${uid}`);

      if (response.data) {
        console.log('User data fetched successfully:', response.data);
        setUserData(response.data);
      }
    } catch (error) {
      console.error('Error fetching user data:', error);
      // Fallback to auth profile data
      if (auth.user) {
        const fallbackData: UserData = {
          name: auth.user.profile.name ||
                auth.user.profile.given_name ||
                auth.user.profile.preferred_username ||
                auth.user.profile.email?.split('@')[0] ||
                'User',
          email: auth.user.profile.email,
          profileImage: 'https://via.placeholder.com/40'
        };
        setUserData(fallbackData);
      }
    } finally {
      setUserDataLoading(false);
    }
  }, [getUserId, auth.isAuthenticated, auth.user]);

  // Fetch user data when component mounts
  useEffect(() => {
    if (auth.isAuthenticated && !auth.isLoading) {
      fetchUserData();
    }
  }, [auth.isAuthenticated, auth.isLoading, fetchUserData]);

  // Initial bookings data
  const initialBookings = [
    {
      id: 1,
      service: 'Computer Services',
      status: 'Cancelled',
      date: '27 Sep 2022, 17:00-18:00',
      amount: '$100.00',
      payment: 'PayPal',
      location: 'Newark, USA',
      provider: 'John Doe',
      email: '<EMAIL>',
      phone: '******-888-8888',
      actions: ['Reschedule'],
      providerId: 'provider-1',
      bookingId: 'booking-1',
    },
    {
      id: 2,
      service: 'Car Repair Services',
      status: 'Completed',
      date: '23 Sep 2022, 10:00-11:00',
      amount: '$50.00',
      payment: 'COD',
      location: 'Alabama, USA',
      provider: 'John Smith',
      email: '<EMAIL>',
      phone: '******-275-5393',
      actions: ['Rebook', 'Add Review'],
      providerId: 'provider-2',
      bookingId: 'booking-2',
    },
    {
      id: 3,
      service: 'Interior Designing',
      status: 'Inprogress',
      date: '22 Sep 2022, 11:00-12:00',
      amount: '$50.00',
      payment: 'PayPal',
      location: 'Washington, DC, USA',
      provider: 'Quentin',
      email: '<EMAIL>',
      phone: '******-810-9218',
      actions: ['Chat', 'Cancel'],
      providerId: 'provider-3',
      bookingId: 'booking-3',
    },
  ];

  // State to manage bookings
  const [bookings, setBookings] = useState<Booking[]>(initialBookings);

  type ButtonColor = 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger';
  type ButtonVariant = 'solid' | 'bordered' | 'light' | 'flat' | 'faded' | 'shadow' | 'ghost';

  // Define a type for booking
  interface Booking {
    id: number;
    service: string;
    status: string;
    date: string;
    amount: string;
    payment: string;
    location: string;
    provider: string;
    email: string;
    phone: string;
    actions: string[];
    providerId?: string;
    bookingId?: string;
  }

  // Function to handle opening the review form
  const handleAddReview = (booking: Booking) => {
    setSelectedBooking(booking);
    setShowReviewForm(true);
  };

  // Function to handle submitting a review
  const handleSubmitReview = async (reviewData: ReviewData) => {
    if (!selectedBooking) {
      console.error('No booking selected for review');
      return;
    }

    try {
      console.log('Review submitted:', reviewData);
      console.log('Image data in review:', {
        imageCount: reviewData.imageNames?.length || 0,
        imageNames: reviewData.imageNames,
        imageUrls: reviewData.imageUrls
      });

      // Prepare review data for API
      const createData: CreateReviewData = {
        providerId: selectedBooking.providerId || `provider-${selectedBooking.id}`,
        serviceId: selectedBooking.id.toString(),
        serviceName: selectedBooking.service,
        bookingId: selectedBooking.bookingId || `booking-${selectedBooking.id}`,
        title: reviewData.title || selectedBooking.service,
        review: reviewData.review,
        comment: reviewData.review, // Backend expects comment field
        rating: reviewData.rating,
        images: reviewData.images,
        imageUrls: reviewData.imageUrls || [],
        imageNames: reviewData.imageNames || [], // Include image names for backend storage
      };

      console.log('Prepared review data for API:', {
        ...createData,
        imageNamesCount: createData.imageNames?.length || 0,
        imageNames: createData.imageNames,
        ratingFields: {
          rating: createData.rating,
          serviceRating: createData.serviceRating,
          qualityRating: createData.qualityRating,
          valueRating: createData.valueRating,
          communicationRating: createData.communicationRating,
          timelinessRating: createData.timelinessRating
        }
      });

      // Backend requires all rating fields - provide defaults using overall rating if not set
      createData.serviceRating = (reviewData.serviceRating && reviewData.serviceRating >= 1 && reviewData.serviceRating <= 4)
        ? Math.round(reviewData.serviceRating)
        : Math.round(reviewData.rating);

      createData.qualityRating = (reviewData.qualityRating && reviewData.qualityRating >= 1 && reviewData.qualityRating <= 4)
        ? Math.round(reviewData.qualityRating)
        : Math.round(reviewData.rating);

      createData.valueRating = (reviewData.valueRating && reviewData.valueRating >= 1 && reviewData.valueRating <= 4)
        ? Math.round(reviewData.valueRating)
        : Math.round(reviewData.rating);

      createData.communicationRating = (reviewData.communicationRating && reviewData.communicationRating >= 1 && reviewData.communicationRating <= 4)
        ? Math.round(reviewData.communicationRating)
        : Math.round(reviewData.rating);

      createData.timelinessRating = (reviewData.timelinessRating && reviewData.timelinessRating >= 1 && reviewData.timelinessRating <= 4)
        ? Math.round(reviewData.timelinessRating)
        : Math.round(reviewData.rating);

      // Submit review to API
      await createReview(createData);

      // Show success message
      toast.success('Review submitted successfully!');

      // Navigate to reviews page
      navigate('/customer/reviews');
    } catch (error) {
      console.error('Error submitting review:', error);
      toast.error('Failed to submit review. Please try again.');
    }
  };

  // Function to handle rescheduling
  const handleReschedule = (booking: Booking) => {
    setSelectedBooking(booking);
    setShowRescheduleForm(true);
  };

  // Function to handle rescheduling submission
  const handleRescheduleSubmit = (rescheduleData: RescheduleData) => {
    try {
      // In a real app, you would send this data to your backend
      console.log('Reschedule data submitted:', rescheduleData);

      // Update the booking with the new date and time
      const updatedBookings = bookings.map(booking => {
        if (booking.id === rescheduleData.bookingId) {
          // Format the date
          const dateOptions: Intl.DateTimeFormatOptions = {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
          };
          const formattedDate = rescheduleData.newDate.toLocaleDateString('en-US', dateOptions);

          // Create the new date string
          const newDateString = `${formattedDate}, ${rescheduleData.newTime}`;

          // Update the booking status to "Rescheduled"
          return {
            ...booking,
            date: newDateString,
            status: 'Rescheduled',
            // Add a notification action to allow user to add to calendar
            actions: [...booking.actions, 'Add to Calendar'].filter(action => action !== 'Reschedule'),
          };
        }
        return booking;
      });

      // Update the bookings state
      setBookings(updatedBookings);

      // Success is now handled in the RescheduleForm component with a visual notification
    } catch (error) {
      console.error('Error rescheduling booking:', error);
      alert('Failed to reschedule booking. Please try again.');
    }
  };

  // Function to handle button actions
  const handleButtonAction = (action: string, booking: Booking) => {
    switch (action) {
      case 'Add Review':
        handleAddReview(booking);
        break;
      case 'Chat':
        navigate('/customer/chat');
        break;
      case 'Reschedule':
        handleReschedule(booking);
        break;
      case 'Rebook':
        // Handle rebook action
        alert('Rebook functionality will be implemented soon');
        break;
      case 'Add to Calendar': {
        // Create calendar event URL (works with Google Calendar)
        const eventTitle = encodeURIComponent(`Appointment: ${booking.service}`);
        const eventDetails = encodeURIComponent(`Provider: ${booking.provider}\nLocation: ${booking.location}\nContact: ${booking.email}, ${booking.phone}`);
        const eventLocation = encodeURIComponent(booking.location);

        // Parse date and time
        const [datePart, timePart] = booking.date.split(',');
        const dateObj = new Date(datePart);

        // Extract start and end times
        let startTime = '', endTime = '';
        if (timePart) {
          const timeRange = timePart.trim();
          const [start, end] = timeRange.split('-');
          startTime = start.trim();
          endTime = end ? end.trim() : '';
        }

        // Create start and end date objects
        const startDate = new Date(dateObj);
        const endDate = new Date(dateObj);

        // Set hours based on time string (simple parsing)
        if (startTime) {
          const [hourMin, period] = startTime.split(' ');
          let hour;
          const minute = hourMin.split(':').map(Number)[1] || 0;
          hour = Number(hourMin.split(':')[0]);
          if (period === 'PM' && hour < 12) hour += 12;
          if (period === 'AM' && hour === 12) hour = 0;
          startDate.setHours(hour, minute, 0);

          // Default end time is 1 hour later if not specified
          if (endTime) {
            const [endHourMin, endPeriod] = endTime.split(' ');
            let endHour;
            const endMinute = endHourMin.split(':').map(Number)[1] || 0;
            endHour = Number(endHourMin.split(':')[0]);
            if (endPeriod === 'PM' && endHour < 12) endHour += 12;
            if (endPeriod === 'AM' && endHour === 12) endHour = 0;
            endDate.setHours(endHour, endMinute, 0);
          } else {
            endDate.setHours(startDate.getHours() + 1, startDate.getMinutes(), 0);
          }
        }

        // Format dates for URL
        const formatDateForCalendar = (date: Date) => {
          return date.toISOString().replace(/-|:|\.\d+/g, '');
        };

        const startDateStr = formatDateForCalendar(startDate);
        const endDateStr = formatDateForCalendar(endDate);

        // Create Google Calendar URL
        const calendarUrl = `https://calendar.google.com/calendar/render?action=TEMPLATE&text=${eventTitle}&details=${eventDetails}&location=${eventLocation}&dates=${startDateStr}/${endDateStr}`;

        // Open in new window
        window.open(calendarUrl, '_blank');
        break;
      }
      case 'Cancel':
        // Handle cancel action
        if (window.confirm('Are you sure you want to cancel this booking?')) {
          // Update the booking status to "Cancelled"
          const updatedBookings = bookings.map(b => {
            if (b.id === booking.id) {
              return {
                ...b,
                status: 'Cancelled',
                actions: ['Reschedule'], // Only allow rescheduling after cancellation
              };
            }
            return b;
          });

          setBookings(updatedBookings);
          alert('Booking cancelled successfully');
        }
        break;
      default:
        break;
    }
  };

  const getButtonStyles = (action: string): { color: ButtonColor; variant: ButtonVariant } => {
    switch (action) {
      case 'Cancel':
        return {
          color: 'danger',
          variant: 'light'
        };
      case 'Chat':
        return {
          color: 'success',
          variant: 'bordered'
        };
      case 'Reschedule':
        return {
          color: 'warning',
          variant: 'bordered'
        };
      case 'Add Review':
        return {
          color: 'secondary',
          variant: 'bordered'
        };
      case 'Rebook':
        return {
          color: 'primary',
          variant: 'solid'
        };
      case 'Add to Calendar':
        return {
          color: 'success',
          variant: 'flat'
        };
      default:
        return {
          color: 'primary',
          variant: 'bordered'
        };
    }
  };

  return (
    <div className="mx-auto p-4">
      <BreadCrumb title="Booking List" item1="Customer" />
      <h2 className="text-2xl sm mb-4 mt-4">Booking List</h2>
      <div className="space-y-6">
        {bookings.map((booking) => (
          <div
            key={booking.id}
            className="bg-white p-4 shadow-md rounded-md flex items-center"
          >
            {/* Placeholder for Image */}
            <div className="w-24 h-24 bg-gray-300 flex-shrink-0 rounded-md"></div>

            {/* Booking Details */}
            <div className="ml-4 flex-1">
              <h3 className="text-lg font-semibold mb-1">{booking.service}</h3>
              <span
                className={`text-sm px-2 py-1 rounded mb-2 inline-block ${
                  booking.status === 'Cancelled'
                    ? 'bg-red-200 text-red-700'
                    : booking.status === 'Completed'
                      ? 'bg-green-200 text-green-700'
                      : booking.status === 'Rescheduled'
                        ? 'bg-amber-200 text-amber-700'
                        : 'bg-blue-200 text-blue-700'
                }`}
              >
                {booking.status}
              </span>
              <p className="text-sm text-gray-600 mb-1">{booking.date}</p>
              <p className="text-sm mb-1">
                <span className="font-semibold">Amount:</span> {booking.amount}{' '}
                <span className="text-blue-600">{booking.payment}</span>
              </p>
              <p className="text-sm mb-1">
                <span className="font-semibold">Location:</span>{' '}
                {booking.location}
              </p>
              <p className="text-sm">
                <span className="font-semibold">Provider:</span>{' '}
                {booking.provider} -{' '}
                <a href={`mailto:${booking.email}`} className="text-blue-600">
                  {booking.email}
                </a>
                , {booking.phone}
              </p>
            </div>

            {/* Actions */}
            <div className="flex flex-col space-y-2">
              {booking.actions.map((action, index) => (
                <CustomButton
                  key={index}
                  label={action}
                  color={getButtonStyles(action).color}
                  variant={getButtonStyles(action).variant}
                  size="sm"
                  radius="sm"
                  className="min-w-[100px] font-medium"
                  onPress={() => handleButtonAction(action, booking)}
                />
              ))}
            </div>
          </div>
        ))}
      </div>

      {/* Review Form Modal */}
      {selectedBooking && showReviewForm && (
        <ReviewForm
          isOpen={showReviewForm}
          onClose={() => setShowReviewForm(false)}
          onSubmit={handleSubmitReview}
          providerId={selectedBooking.providerId || `provider-${selectedBooking.id}`}
          serviceId={selectedBooking.id.toString()}
          bookingId={selectedBooking.bookingId || `booking-${selectedBooking.id}`}
          serviceName={selectedBooking.service}
          initialData={{
            id: `review-${Date.now()}`,
            providerId: selectedBooking.providerId || `provider-${selectedBooking.id}`,
            serviceId: selectedBooking.id.toString(),
            serviceName: selectedBooking.service,
            rating: 0,
            title: `Review for ${selectedBooking.service}`,
            review: '',
            images: [],
            imageUrls: [],
            imageNames: [] // Include empty image names array
          }}
        />
      )}

      {/* Reschedule Form Modal */}
      {selectedBooking && showRescheduleForm && (
        <RescheduleForm
          isOpen={showRescheduleForm}
          onClose={() => setShowRescheduleForm(false)}
          onSubmit={handleRescheduleSubmit}
          bookingDetails={{
            id: selectedBooking.id,
            service: selectedBooking.service,
            date: selectedBooking.date,
            provider: selectedBooking.provider
          }}
        />
      )}
    </div>
  );
};

export default BookingList;
